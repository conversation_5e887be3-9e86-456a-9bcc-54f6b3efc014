import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { z } from "zod";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { sanitizeInput, withIframeSecurity } from "../../middleware/security.js";
import { PayrixService } from "../../service/payrix.service.js";
import { IntegrationTokenRequest } from "../../types/integration-token.types.js";
import { tokenRequestSchema } from "./schemas/integration-token.schema.js";
import {
  generateSecureToken,
  storeToken,
  validateToken as validateTokenService,
  markTokenAsUsed as markTokenAsUsedService,
} from "./services/integration-token.service.js";
import { validateIntegrationTokenRequest } from "./validators/integration-token.validator.js";
import { buildTokenData, buildTokenResponse } from "./utils/token-config-builder.js";

export const validateToken = validateTokenService;
export const markTokenAsUsed = markTokenAsUsedService;

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Generate integration token request", {
      method: event.httpMethod,
      path: event.path,
      hasBody: !!event.body,
      contentType: event.headers?.["content-type"] || event.headers?.["Content-Type"],
    });

    if (!event.body) {
      return createIframeResponse(400, {
        error: "Request body is required",
        message: "Please provide merchantId and description",
      });
    }

    let requestData: IntegrationTokenRequest;
    try {
      const parsedBody = JSON.parse(event.body);
      requestData = tokenRequestSchema.parse(parsedBody);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(400, {
          error: "Validation failed",
          message: "Invalid request data",
          details: error.errors,
        });
      }
      throw error;
    }

    const {
      merchantId,
      description,
      amount = 1000,
      returnUrl,
      expiresIn = 60,
      currency = "USD",
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
    } = requestData;

    const validationErrors = validateIntegrationTokenRequest(requestData);
    if (validationErrors.length > 0) {
      const firstError = validationErrors[0];
      return createIframeResponse(firstError.statusCode || 400, {
        error: firstError.error,
        message: firstError.message,
        details: firstError.details,
      });
    }

    const sanitizedDescription = sanitizeInput(description);

    logger.info("Processing integration token request", {
      merchantId,
      description: sanitizedDescription,
      amount,
      returnUrl,
      expiresIn,
    });

    const payrixService = new PayrixService();
    const validation = await payrixService.validateMerchantById(merchantId);

    if (!validation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: validation.error,
      });

      return createIframeResponse(404, {
        error: "Merchant validation failed",
        message: validation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    logger.info("Merchant validation successful, generating integration token", {
      merchantId,
      merchantName: validation.merchant?.dba || validation.merchant?.name,
    });

    const token = generateSecureToken();
    const expiresAt = new Date(Date.now() + expiresIn * 60 * 1000);

    const tokenData = buildTokenData(
      merchantId,
      sanitizedDescription,
      amount,
      expiresAt,
      returnUrl,
      {
        currency,
        items,
        taxAmount,
        shippingAmount,
        dutyAmount,
        orderNumber,
        invoiceNumber,
        customerCode,
        orderDiscount,
      },
      validation.merchant?.address || validation.merchant?.email || validation.merchant?.phone
        ? {
            address: validation.merchant?.address
              ? {
                  line1: validation.merchant.address.line1,
                  line2: validation.merchant.address.line2,
                  city: validation.merchant.address.city,
                  state: validation.merchant.address.state,
                  zip: validation.merchant.address.zip,
                  country: validation.merchant.address.country || "US",
                }
              : undefined,
            email: validation.merchant?.email,
            phone: validation.merchant?.phone,
          }
        : undefined
    );

    await storeToken(token, tokenData);

    const response = buildTokenResponse(token, expiresAt, {
      id: merchantId,
      name: validation.merchant?.dba || validation.merchant?.name,
      status: validation.merchant?.status,
    });

    logger.info("Integration token generated successfully", {
      token: token.substring(0, 8) + "...",
      expiresAt: expiresAt.toISOString(),
      merchantId,
    });

    return createIframeResponse(200, {
      success: true,
      message: "Integration token generated successfully",
      data: response,
    });
  } catch (error) {
    logger.error("Error generating integration token", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to generate integration token",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);
