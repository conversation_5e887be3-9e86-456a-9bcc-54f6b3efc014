import { APIGatewayProxyHandler } from "aws-lambda";
import { AxiosError } from "axios";
import { PayrixService } from "../../service/payrix.service.js";
import { validateOnboardingRequest } from "./schemas/onboarding.schema.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
// import { checkExistingMerchant } from "./utils/merchant-validation.js"; // Commented out as per user request
import { handlePayrixError, handleValidationError } from "./utils/error-handling.js";
import { createUserAccountIfRequested } from "./services/user-account.service.js";
import { buildSuccessResponse } from "./utils/response-builders.js";

interface VerificationFile {
  name: string;
  size: number;
  type: string;
  content: string; // Base64 encoded
}

interface VerificationUploadResult {
  success: boolean;
  noteId?: string;
  documentId?: string;
  error?: string;
}

async function processBankVerification(
  payrixService: PayrixService,
  entityId: string,
  verificationFile: VerificationFile,
  requestId: string,
  verificationMethod: string,
  loginId?: string
): Promise<VerificationUploadResult> {
  if (verificationMethod !== "manual") {
    logger.info("Skipping bank verification upload - not manual verification", {
      requestId,
      entityId,
      verificationMethod,
    });
    return {
      success: true,
      noteId: undefined,
      documentId: undefined,
    };
  }

  if (!verificationFile) {
    logger.warn("Manual verification selected but no file provided", {
      requestId,
      entityId,
      verificationMethod,
    });
    throw new Error("Manual verification requires a file upload");
  }

  // Enhanced file validation
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "application/pdf"];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(verificationFile.type.toLowerCase())) {
    logger.error("Invalid file type for bank verification", {
      requestId,
      entityId,
      fileType: verificationFile.type,
      allowedTypes,
    });
    throw new Error("Invalid file type. Only JPEG, PNG, and PDF files are allowed for bank verification.");
  }

  if (verificationFile.size > maxFileSize) {
    logger.error("File size exceeds limit for bank verification", {
      requestId,
      entityId,
      fileSize: verificationFile.size,
      maxFileSize,
    });
    throw new Error("File size exceeds 10MB limit for bank verification documents.");
  }

  logger.info("Starting priority bank verification upload process for manual verification", {
    requestId,
    entityId,
    verificationMethod,
    fileName: verificationFile.name,
    fileSize: verificationFile.size,
    fileType: verificationFile.type,
    hasLoginContext: !!loginId,
    loginId: loginId ? `${loginId.substring(0, 8)}...` : undefined, // Partial logging for security
  });

  try {
    // Step 1: Create a note for bank verification with enhanced description
    if (!loginId) {
      logger.warn("Creating note without login context - user account creation may have failed", {
        requestId,
        entityId,
        step: "note_creation_without_login_context",
      });
    }

    logger.info("Creating verification note in Payrix", {
      requestId,
      entityId,
      hasLoginContext: !!loginId,
      step: "note_creation_start",
    });

    const noteData = {
      entity: entityId,
      note: `Bank Account Verification Document - Voided Check uploaded during merchant onboarding for entity ${entityId}`,
      type: "note",
      ...(loginId && { login: loginId }), // Include login context if available
    };

    // Log login context status for debugging
    if (!loginId) {
      logger.warn("Creating bank verification note without login context", {
        requestId,
        entityId,
        verificationMethod,
        impact: "Note will be created without user association",
        recommendation: "Ensure user account creation succeeds before bank verification",
      });
    }

    logger.info("Note data prepared", {
      requestId,
      entityId,
      noteType: noteData.type,
      noteLength: noteData.note.length,
      hasLoginContext: !!loginId,
      loginId: loginId ? `${loginId.substring(0, 8)}...` : undefined, // Partial logging for security
      step: "note_data_prepared",
    });

    const noteResponse = await payrixService.createNote(noteData);

    if (!noteResponse?.id) {
      logger.error("Failed to create verification note - no ID returned from Payrix", {
        requestId,
        entityId,
        noteResponse,
      });
      throw new Error("Failed to create verification note - Payrix did not return note ID");
    }

    logger.info("Verification note created successfully", {
      requestId,
      entityId,
      noteId: noteResponse.id,
    });

    // Step 2: Create note document with enhanced description
    logger.info("Uploading verification document to Payrix", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      fileName: verificationFile.name,
      step: "document_upload_start",
    });

    const fileBuffer = Buffer.from(verificationFile.content, "base64");

    logger.info("File buffer created", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      bufferSize: fileBuffer.length,
      originalSize: verificationFile.size,
      step: "file_buffer_created",
    });

    const documentData = {
      note: noteResponse.id,
      file: {
        filename: verificationFile.name,
        content: fileBuffer,
        contentType: verificationFile.type,
      },
      description: `Voided check for bank account verification - ${verificationFile.name}`,
    };

    logger.info("Document data prepared", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      documentData: {
        ...documentData,
        file: {
          ...documentData.file,
          content: `[Buffer ${fileBuffer.length} bytes]`, // Don't log the actual buffer
        },
      },
      step: "document_data_prepared",
    });

    const documentResponse = await payrixService.createNoteDocument(documentData);

    if (!documentResponse?.id) {
      logger.error("Failed to create note document - no ID returned from Payrix", {
        requestId,
        entityId,
        noteId: noteResponse.id,
        documentResponse,
      });
      throw new Error("Failed to create note document - Payrix did not return document ID");
    }

    logger.info("Bank verification upload completed successfully - priority operation complete", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      documentId: documentResponse.id,
      fileName: verificationFile.name,
      fileSize: verificationFile.size,
    });

    return {
      success: true,
      noteId: noteResponse.id,
      documentId: documentResponse.id,
    };
  } catch (error) {
    // Enhanced error handling with specific error types
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 400) {
      logger.error("Bad request to Payrix API for bank verification", {
        requestId,
        entityId,
        error: axiosError.response.data,
      });
      throw new Error("Invalid data provided for bank verification upload");
    } else if (axiosError.response?.status === 401) {
      logger.error("Authentication failed with Payrix API for bank verification", {
        requestId,
        entityId,
      });
      throw new Error("Authentication failed with Payrix API");
    } else if (axiosError.response?.status === 404) {
      logger.error("Entity not found in Payrix for bank verification", {
        requestId,
        entityId,
      });
      throw new Error(`Entity ${entityId} not found in Payrix system`);
    }

    logger.error("Priority bank verification upload failed", {
      requestId,
      entityId,
      fileName: verificationFile.name,
      error,
    });
    throw error;
  }
}

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    const body = JSON.parse(event.body || "{}");
    const validation = validateOnboardingRequest(body);

    if (!validation.success || !validation.data) {
      return handleValidationError(validation.errors || ["Unknown validation error"], requestId);
    }

    const data = validation.data;

    if (!data.clientIp) {
      return handleValidationError(["Client IP is required"], requestId);
    }

    logger.info("Processing direct Payrix submission", {
      requestId,
      email: data.email,
      legalName: data.name,
      clientIp: data.clientIp,
    });

    // const merchantCheck = await checkExistingMerchant(data.email, data.ein, requestId);
    // if (merchantCheck.exists && merchantCheck.response) {
    //   return merchantCheck.response;
    // }

    const payrixService = new PayrixService();
    let payrixResponse: { id: string; [key: string]: unknown };
    let payrixEntityId: string;

    try {
      logger.info("Creating merchant in Payrix (direct integration)", { requestId });

      payrixResponse = await payrixService.createMerchant(data);
      payrixEntityId = payrixResponse?.id;

      if (!payrixEntityId) {
        throw new Error("Payrix response did not contain entity ID");
      }

      logger.info("Payrix merchant created successfully (direct integration)", {
        requestId,
        payrixEntityId,
      });

      // Create user account first to get login context for note creation
      let userAccountResult;
      try {
        logger.info("Processing user account creation (before bank verification)", {
          requestId,
          payrixEntityId,
        });

        userAccountResult = await createUserAccountIfRequested(data, payrixEntityId, requestId);

        if (userAccountResult.success) {
          logger.info("User account created successfully - login context available for bank verification", {
            requestId,
            payrixEntityId,
            userId: userAccountResult.data?.id,
            hasLoginId: !!userAccountResult.data?.id,
          });
        } else {
          logger.warn("User account creation failed - bank verification will proceed without login context", {
            requestId,
            payrixEntityId,
            error: userAccountResult.error,
            impact: "Bank verification notes will be created without user login context",
          });
        }
      } catch (userAccountError) {
        logger.error("User account creation failed with exception - bank verification will proceed without login context", {
          requestId,
          payrixEntityId,
          error: userAccountError,
          impact: "Bank verification notes will be created without user login context",
        });
        userAccountResult = {
          success: false,
          error: "User account creation failed",
        };
      }

      let verificationUploadResult = null;

      if (data.bankVerification?.verificationMethod) {
        if (data.bankVerification.verificationMethod === "manual" && data.bankVerification.verificationFile) {
          try {
            // Verify user account creation was successful before proceeding with bank verification
            if (!userAccountResult?.success || !userAccountResult?.data?.id) {
              logger.warn("User account creation failed or incomplete - proceeding with bank verification without login context", {
                requestId,
                payrixEntityId,
                userAccountSuccess: userAccountResult?.success,
                hasLoginId: !!userAccountResult?.data?.id,
                userAccountError: userAccountResult?.error,
              });
            }

            logger.info("Processing bank verification upload (priority operation)", {
              requestId,
              payrixEntityId,
              verificationMethod: data.bankVerification.verificationMethod,
              fileName: data.bankVerification.verificationFile.name,
              fileSize: data.bankVerification.verificationFile.size,
              userAccountId: userAccountResult?.data?.id,
              hasLoginContext: !!userAccountResult?.data?.id,
            });

            verificationUploadResult = await processBankVerification(
              payrixService,
              payrixEntityId,
              data.bankVerification.verificationFile,
              requestId,
              data.bankVerification.verificationMethod,
              userAccountResult?.data?.id // Pass login ID for note creation (may be undefined)
            );

            logger.info("Bank verification upload completed successfully", {
              requestId,
              payrixEntityId,
              success: verificationUploadResult.success,
              noteId: verificationUploadResult.noteId,
              documentId: verificationUploadResult.documentId,
            });
          } catch (verificationError) {
            const errorMessage = verificationError instanceof Error ? verificationError.message : String(verificationError);
            logger.error("Bank verification upload failed", {
              requestId,
              payrixEntityId,
              error: verificationError,
              errorMessage,
              errorStack: verificationError instanceof Error ? verificationError.stack : undefined,
            });
            // Don't fail the entire onboarding if verification upload fails
            verificationUploadResult = {
              success: false,
              error: `Bank verification failed: ${errorMessage}`,
            };
          }
        } else if (data.bankVerification.verificationMethod === "plaid") {
          // Plaid verification - no file upload or Payrix API calls needed
          logger.info("Skipping bank verification upload - Plaid verification selected", {
            requestId,
            payrixEntityId,
            verificationMethod: data.bankVerification.verificationMethod,
          });
          verificationUploadResult = {
            success: true,
            noteId: undefined,
            documentId: undefined,
          };
        } else {
          // Unknown verification method or manual without file
          logger.warn("Invalid bank verification configuration", {
            requestId,
            payrixEntityId,
            verificationMethod: data.bankVerification.verificationMethod,
            hasFile: !!data.bankVerification.verificationFile,
          });
          verificationUploadResult = {
            success: false,
            error: "Invalid bank verification configuration",
          };
        }
      }

      return buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null,
        verificationUploadResult
      );
    } catch (payrixError) {
      return handlePayrixError(payrixError as Error, requestId);
    }
  } catch (error) {
    const err = error as Error;
    logger.error("Error in onboard handler", {
      requestId,
      error: err.message,
      stack: err.stack,
    });

    return createErrorResponse(500, "Internal Server Error", err.message);
  }
};
