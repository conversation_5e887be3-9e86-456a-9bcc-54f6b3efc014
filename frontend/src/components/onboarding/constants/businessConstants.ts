export const BUSINESS_TYPES = [
  { value: 1, label: "Sole Proprietor" },
  { value: 2, label: "Corporation" },
  { value: 3, label: "Limited Liability Company" },
  { value: 4, label: "Partnership" },
];

export const ENTITY_CLASSIFICATION = [
  { value: 0, label: "Private Entity" },
  { value: 1, label: "Public Entity" },
];

export const US_STATES = [
  "AL",
  "AK",
  "AZ",
  "AR",
  "CA",
  "CO",
  "CT",
  "DE",
  "FL",
  "GA",
  "HI",
  "ID",
  "IL",
  "IN",
  "IA",
  "KS",
  "KY",
  "LA",
  "ME",
  "MD",
  "MA",
  "MI",
  "MN",
  "MS",
  "MO",
  "MT",
  "NE",
  "NV",
  "NH",
  "NJ",
  "NM",
  "NY",
  "NC",
  "ND",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VT",
  "VA",
  "WA",
  "WV",
  "WI",
  "WY",
];

export const BUSINESS_TYPE_CHECKS = {
  isSoleProprietor: (type: number) => type === 1,
  isCorporation: (type: number) => type === 2,
  isLLC: (type: number) => type === 3,
  isPartnership: (type: number) => type === 4,
  requiresCorporateStructure: (type: number) => type === 2 || type === 3 || type === 4,
};
